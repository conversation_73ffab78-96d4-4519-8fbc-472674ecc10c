# 墨尔本停车位查找应用演示脚本

## 应用概述
这是一个完整的iOS应用，显示墨尔本的实时停车位信息，具有现代化的用户界面和交互功能。

## 主要功能演示

### 1. 启动应用
- 应用启动后显示以墨尔本为中心的全屏地图
- 地图上显示8个停车位，用红色圆形标记表示
- 底部显示折叠状态的搜索栏

### 2. 地图交互
- 可以缩放和平移地图查看不同区域
- 点击红色停车位标记会显示详细信息弹窗
- 弹窗显示停车场名称和可用车位数量

### 3. 底部面板交互
- **上拉手势**：在搜索栏上向上滑动可展开停车位列表
- **点击手势**：点击顶部的小把手也可以展开/折叠面板
- **平滑动画**：展开和折叠过程有流畅的动画效果

### 4. 交通方式选择
展开面板后可以看到四种交通方式：
- 🚗 **汽车** (默认选中)
- 🚲 **自行车**
- 🚚 **货车**
- ⋯ **更多**

选择不同交通方式会更新标题文字。

### 5. 停车位列表
展开状态下显示详细的停车位列表，每个项目包含：
- **停车场图片**：彩色编码的占位符图片
  - 绿色：车位充足 (>50个)
  - 橙色：车位适中 (20-50个)
  - 红色：车位紧张 (<20个)
- **停车场名称**：粗体显示
- **地址**：带位置图标的完整地址
- **价格**：蓝色文字显示每小时费用
- **可用车位**：灰色文字显示剩余车位数

### 6. 搜索功能
- 在搜索栏中输入关键词可以过滤停车位
- 支持按停车场名称或地址搜索
- 实时更新搜索结果

### 7. 列表与地图联动
- 点击列表中的停车位会在地图上居中显示该位置
- 自动选中对应的地图标记
- 地图会缩放到合适的级别

## 示例停车位数据

应用包含8个墨尔本CBD区域的示例停车位：

1. **Collins Street Car Park** - $8.50/小时, 45个车位
2. **Flinders Lane Parking** - $12.00/小时, 23个车位
3. **Queen Victoria Market Parking** - $6.00/小时, 78个车位
4. **Southern Cross Station Parking** - $15.00/小时, 12个车位
5. **Federation Square Parking** - $10.50/小时, 34个车位
6. **Melbourne Central Parking** - $14.00/小时, 56个车位
7. **Bourke Street Mall Parking** - $11.00/小时, 19个车位
8. **Docklands Stadium Parking** - $7.50/小时, 89个车位

## 技术特性

### 用户界面
- 使用UIKit和MapKit框架
- 自定义底部面板组件
- 响应式设计，支持不同屏幕尺寸
- 现代化的iOS设计语言

### 交互体验
- 手势识别器支持上拉操作
- 平滑的动画过渡
- 直观的用户界面元素

### 数据管理
- 结构化的停车位数据模型
- 可扩展的数据架构
- 支持实时数据更新

### 位置服务
- 集成CoreLocation框架
- 请求用户位置权限
- 自动定位到用户附近区域

## 运行要求
- iOS 18.5+
- Xcode 16.0+
- iPhone或iPad设备/模拟器

## 构建状态
✅ 项目构建成功
✅ 所有组件正常工作
✅ 无编译错误或警告

这个应用展示了现代iOS开发的最佳实践，包括MVC架构、自定义UI组件、手势识别、地图集成和数据管理。
