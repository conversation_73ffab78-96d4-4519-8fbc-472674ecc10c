# Melbourne Parking Finder iOS App

一个显示墨尔本实时停车位信息的iOS应用程序。

## 功能特性

### 主界面
- 全屏地图视图，使用MapKit框架，以墨尔本为中心
- 停车位以红色圆形标记显示在地图上
- 底部搜索栏，带有蓝色麦克风按钮用于语音输入
- 搜索栏位于屏幕底部的折叠状态

### 交互元素
- 在搜索栏上实现上拉手势以显示停车位列表
- 上拉时显示交通方式选择器，包含图标：汽车、自行车、货车、更多
- 折叠和展开状态之间的平滑动画过渡

### 停车位列表视图（展开状态）
- 标题："附近的汽车停车位"（根据选择的交通方式动态变化）
- 列表项显示：
  - 停车场照片（矩形缩略图）
  - 停车场名称
  - 带位置图标的街道地址
  - 价格：蓝色文本显示"$X/小时"
  - 可用车位：灰色文本显示"XX个车位"
  - 所有文本左对齐，间距合适

## 技术实现

### 框架和技术
- 使用UIKit和MapKit框架
- 实现自定义底部面板/面板组件
- 添加平移手势识别器用于上拉交互
- 创建自定义地图标注用于停车位
- 使用集合视图或表格视图显示停车位列表
- 实现适当的Auto Layout约束

### 数据结构
```swift
struct ParkingSpot {
    let name: String
    let address: String
    let pricePerHour: Double
    let availableSpots: Int
    let coordinate: CLLocationCoordinate2D
    let imageURL: String?
}
```

### UI样式
- 简洁现代的设计，白色背景
- 蓝色强调色（#007AFF）用于交互元素
- 灰色文本用于次要信息
- 底部面板圆角设计
- 使用系统字体和适当的字重

## 项目结构

```
XYZ Car Parking/
├── Models/
│   └── ParkingSpot.swift          # 停车位数据模型
├── Views/
│   ├── ParkingAnnotation.swift    # 自定义地图标注
│   ├── TransportModeSelector.swift # 交通方式选择器
│   ├── ParkingListCell.swift      # 停车位列表单元格
│   └── BottomSheetViewController.swift # 底部面板控制器
├── Controllers/
│   └── MainViewController.swift   # 主视图控制器
├── Extensions/
│   └── UIImageView+Extensions.swift # 图片加载扩展
├── ContentView.swift              # SwiftUI包装器
└── Info.plist                     # 位置权限配置
```

## 主要组件

### MainViewController
- 管理地图视图和底部面板
- 处理位置服务和地图交互
- 协调停车位数据显示

### BottomSheetViewController
- 实现可拉起的底部面板
- 包含搜索功能和停车位列表
- 支持平滑的动画过渡

### ParkingAnnotation
- 自定义地图标注视图
- 红色圆形标记设计
- 支持点击显示详细信息

### TransportModeSelector
- 交通方式选择组件
- 支持汽车、自行车、货车等模式
- 动态更新列表标题

## 示例数据

应用包含墨尔本CBD区域的示例停车数据：
- Collins Street Car Park
- Flinders Lane Parking
- Queen Victoria Market Parking
- Southern Cross Station Parking
- Federation Square Parking
- Melbourne Central Parking
- Bourke Street Mall Parking
- Docklands Stadium Parking

## 权限要求

- 位置访问权限：用于显示用户位置和附近停车位
- 配置在Info.plist中的NSLocationWhenInUseUsageDescription

## 运行要求

- iOS 14.0+
- Xcode 12.0+
- Swift 5.0+

## 安装和运行

1. 克隆或下载项目
2. 在Xcode中打开 `XYZ Car Parking.xcodeproj`
3. 选择目标设备或模拟器
4. 点击运行按钮或按 Cmd+R

## 功能演示

1. **地图视图**：启动应用后会看到以墨尔本为中心的地图，红色圆点标记停车位
2. **底部搜索栏**：点击或上拉搜索栏可展开停车位列表
3. **交通方式选择**：在展开状态下可选择不同的交通方式
4. **停车位列表**：显示详细的停车信息，包括价格和可用车位数
5. **地图交互**：点击列表中的停车位会在地图上居中显示该位置

## 未来改进

- 集成真实的停车位API
- 添加导航功能
- 实现停车位预订
- 添加用户评价和评分
- 支持离线地图
- 添加停车历史记录
