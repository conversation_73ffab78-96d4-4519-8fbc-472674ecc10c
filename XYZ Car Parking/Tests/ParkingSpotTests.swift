//
//  ParkingSpotTests.swift
//  XYZ Car Parking Tests
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import XCTest
import CoreLocation
@testable import XYZ_Car_Parking

class ParkingSpotTests: XCTestCase {
    
    func testParkingSpotInitialization() {
        // Given
        let coordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)
        let name = "Test Parking"
        let address = "123 Test Street"
        let pricePerHour = 10.50
        let availableSpots = 25
        
        // When
        let parkingSpot = ParkingSpot(
            name: name,
            address: address,
            pricePerHour: pricePerHour,
            availableSpots: availableSpots,
            coordinate: coordinate
        )
        
        // Then
        XCTAssertEqual(parkingSpot.name, name)
        XCTAssertEqual(parkingSpot.address, address)
        XCTAssertEqual(parkingSpot.pricePerHour, pricePerHour)
        XCTAssertEqual(parkingSpot.availableSpots, availableSpots)
        XCTAssertEqual(parkingSpot.coordinate.latitude, coordinate.latitude, accuracy: 0.0001)
        XCTAssertEqual(parkingSpot.coordinate.longitude, coordinate.longitude, accuracy: 0.0001)
        XCTAssertNotNil(parkingSpot.id)
    }
    
    func testSampleDataExists() {
        // Given & When
        let sampleData = ParkingSpot.sampleData
        
        // Then
        XCTAssertFalse(sampleData.isEmpty, "Sample data should not be empty")
        XCTAssertEqual(sampleData.count, 8, "Should have 8 sample parking spots")
        
        // Test first sample spot
        let firstSpot = sampleData.first!
        XCTAssertEqual(firstSpot.name, "Collins Street Car Park")
        XCTAssertEqual(firstSpot.pricePerHour, 8.50)
        XCTAssertEqual(firstSpot.availableSpots, 45)
    }
    
    func testSampleDataCoordinatesAreInMelbourne() {
        // Given
        let melbourneCenter = CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)
        let maxDistanceFromCenter: Double = 10000 // 10km in meters
        
        // When & Then
        for parkingSpot in ParkingSpot.sampleData {
            let spotLocation = CLLocation(latitude: parkingSpot.coordinate.latitude, longitude: parkingSpot.coordinate.longitude)
            let centerLocation = CLLocation(latitude: melbourneCenter.latitude, longitude: melbourneCenter.longitude)
            let distance = spotLocation.distance(from: centerLocation)
            
            XCTAssertLessThan(distance, maxDistanceFromCenter, 
                             "\(parkingSpot.name) should be within 10km of Melbourne center")
        }
    }
    
    func testTransportModeEnum() {
        // Given & When
        let allModes = TransportMode.allCases
        
        // Then
        XCTAssertEqual(allModes.count, 4)
        XCTAssertTrue(allModes.contains(.car))
        XCTAssertTrue(allModes.contains(.bike))
        XCTAssertTrue(allModes.contains(.van))
        XCTAssertTrue(allModes.contains(.more))
        
        // Test icons
        XCTAssertEqual(TransportMode.car.icon, "car.fill")
        XCTAssertEqual(TransportMode.bike.icon, "bicycle")
        XCTAssertEqual(TransportMode.van.icon, "truck.box.fill")
        XCTAssertEqual(TransportMode.more.icon, "ellipsis")
        
        // Test parking titles
        XCTAssertEqual(TransportMode.car.parkingTitle, "Car Parking Nearby")
        XCTAssertEqual(TransportMode.bike.parkingTitle, "Bike Parking Nearby")
        XCTAssertEqual(TransportMode.van.parkingTitle, "Van Parking Nearby")
        XCTAssertEqual(TransportMode.more.parkingTitle, "Other Parking Nearby")
    }
}
