//
//  MainViewController.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit
import MapKit
import CoreLocation

class MainViewController: UIViewController {
    
    // MARK: - Properties
    private let mapView: MKMapView = {
        let mapView = MKMapView()
        mapView.translatesAutoresizingMaskIntoConstraints = false
        return mapView
    }()
    
    private var bottomSheetViewController: BottomSheetViewController!
    private let locationManager = CLLocationManager()
    private var parkingSpots: [ParkingSpot] = []
    
    // Melbourne coordinates
    private let melbourneCoordinate = CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631)
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
        setupMapView()
        setupBottomSheet()
        setupLocationManager()
        loadParkingData()
    }
    
    // MARK: - Setup Methods
    private func setupView() {
        view.backgroundColor = UIColor.systemBackground
        
        view.addSubview(mapView)
        
        NSLayoutConstraint.activate([
            mapView.topAnchor.constraint(equalTo: view.topAnchor),
            mapView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            mapView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            mapView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .none
        
        // Set initial region to Melbourne
        let region = MKCoordinateRegion(
            center: melbourneCoordinate,
            latitudinalMeters: 5000,
            longitudinalMeters: 5000
        )
        mapView.setRegion(region, animated: false)
        
        // Register annotation view
        mapView.register(ParkingAnnotationView.self, forAnnotationViewWithReuseIdentifier: ParkingAnnotationView.identifier)
    }
    
    private func setupBottomSheet() {
        bottomSheetViewController = BottomSheetViewController()
        bottomSheetViewController.delegate = self
        
        addChild(bottomSheetViewController)
        view.addSubview(bottomSheetViewController.view)
        bottomSheetViewController.didMove(toParent: self)
        
        bottomSheetViewController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            bottomSheetViewController.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            bottomSheetViewController.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            bottomSheetViewController.view.topAnchor.constraint(equalTo: view.topAnchor),
            bottomSheetViewController.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyBest
        
        // Request location permission
        switch locationManager.authorizationStatus {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        default:
            break
        }
    }
    
    private func loadParkingData() {
        parkingSpots = ParkingSpot.sampleData
        addParkingAnnotations()
        bottomSheetViewController.updateParkingSpots(parkingSpots)
    }
    
    private func addParkingAnnotations() {
        let annotations = parkingSpots.map { ParkingAnnotation(parkingSpot: $0) }
        mapView.addAnnotations(annotations)
    }
    
    private func centerMapOnParking(_ parkingSpot: ParkingSpot) {
        let region = MKCoordinateRegion(
            center: parkingSpot.coordinate,
            latitudinalMeters: 1000,
            longitudinalMeters: 1000
        )
        mapView.setRegion(region, animated: true)
        
        // Find and select the annotation
        for annotation in mapView.annotations {
            if let parkingAnnotation = annotation as? ParkingAnnotation,
               parkingAnnotation.parkingSpot.id == parkingSpot.id {
                mapView.selectAnnotation(parkingAnnotation, animated: true)
                break
            }
        }
    }
}

// MARK: - MKMapViewDelegate
extension MainViewController: MKMapViewDelegate {
    func mapView(_ mapView: MKMapView, viewFor annotation: MKAnnotation) -> MKAnnotationView? {
        // Don't customize user location annotation
        if annotation is MKUserLocation {
            return nil
        }
        
        guard annotation is ParkingAnnotation else {
            return nil
        }
        
        let annotationView = mapView.dequeueReusableAnnotationView(
            withIdentifier: ParkingAnnotationView.identifier,
            for: annotation
        ) as? ParkingAnnotationView
        
        return annotationView
    }
    
    func mapView(_ mapView: MKMapView, annotationView view: MKAnnotationView, calloutAccessoryControlTapped control: UIControl) {
        guard let parkingAnnotation = view.annotation as? ParkingAnnotation else {
            return
        }
        
        // Handle callout accessory tap - could show more details or navigate
        let parkingSpot = parkingAnnotation.parkingSpot
        print("Selected parking: \(parkingSpot.name)")
    }
}

// MARK: - CLLocationManagerDelegate
extension MainViewController: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        // Update map region to user's location if it's in Melbourne area
        let melbourneLocation = CLLocation(latitude: melbourneCoordinate.latitude, longitude: melbourneCoordinate.longitude)
        let distance = location.distance(from: melbourneLocation)
        
        if distance < 50000 { // Within 50km of Melbourne
            let region = MKCoordinateRegion(
                center: location.coordinate,
                latitudinalMeters: 5000,
                longitudinalMeters: 5000
            )
            mapView.setRegion(region, animated: true)
        }
        
        // Stop updating location to save battery
        locationManager.stopUpdatingLocation()
    }
    
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            locationManager.startUpdatingLocation()
        case .denied, .restricted:
            // Handle denied location access
            print("Location access denied")
        default:
            break
        }
    }
}

// MARK: - BottomSheetDelegate
extension MainViewController: BottomSheetDelegate {
    func bottomSheetDidChangeState(_ bottomSheet: BottomSheetViewController, state: BottomSheetState) {
        // Handle bottom sheet state changes if needed
        // For example, you could adjust map camera padding
        switch state {
        case .collapsed:
            mapView.layoutMargins = UIEdgeInsets(top: 0, left: 0, bottom: 120, right: 0)
        case .expanded:
            mapView.layoutMargins = UIEdgeInsets(top: 0, left: 0, bottom: 500, right: 0)
        }
    }
    
    func bottomSheetDidSelectParking(_ bottomSheet: BottomSheetViewController, parkingSpot: ParkingSpot) {
        centerMapOnParking(parkingSpot)
    }
}
