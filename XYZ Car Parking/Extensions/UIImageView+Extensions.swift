//
//  UIImageView+Extensions.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

extension UIImageView {
    func setPlaceholderImage(for parkingSpot: ParkingSpot) {
        // Create a placeholder image with parking garage icon
        let config = UIImage.SymbolConfiguration(pointSize: 30, weight: .light)
        let placeholderImage = UIImage(systemName: "car.garage", withConfiguration: config)
        
        // Create a colored background
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 80, height: 60))
        let backgroundImage = renderer.image { context in
            // Set background color based on availability
            let backgroundColor: UIColor
            if parkingSpot.availableSpots > 50 {
                backgroundColor = UIColor.systemGreen.withAlphaComponent(0.2)
            } else if parkingSpot.availableSpots > 20 {
                backgroundColor = UIColor.systemOrange.withAlphaComponent(0.2)
            } else {
                backgroundColor = UIColor.systemRed.withAlphaComponent(0.2)
            }
            
            backgroundColor.setFill()
            context.fill(CGRect(origin: .zero, size: CGSize(width: 80, height: 60)))
            
            // Draw the icon in the center
            if let icon = placeholderImage {
                let iconSize = CGSize(width: 30, height: 30)
                let iconRect = CGRect(
                    x: (80 - iconSize.width) / 2,
                    y: (60 - iconSize.height) / 2,
                    width: iconSize.width,
                    height: iconSize.height
                )
                
                UIColor.systemGray.setFill()
                icon.draw(in: iconRect)
            }
        }
        
        self.image = backgroundImage
        self.contentMode = .scaleAspectFill
        self.clipsToBounds = true
        self.layer.cornerRadius = 8
    }
}

// MARK: - Image Loading Helper
extension UIImageView {
    func loadImage(from urlString: String?, placeholder: UIImage? = nil) {
        // Set placeholder first
        self.image = placeholder
        
        guard let urlString = urlString,
              let url = URL(string: urlString) else {
            return
        }
        
        // Simple image loading - in a real app, you'd use a proper image loading library
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let data = data,
                  let image = UIImage(data: data),
                  error == nil else {
                return
            }
            
            DispatchQueue.main.async {
                self?.image = image
            }
        }.resume()
    }
}
