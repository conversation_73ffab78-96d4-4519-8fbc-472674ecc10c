//
//  UIImageView+Extensions.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

extension UIImageView {
    func setPlaceholderImage(for parkingSpot: ParkingSpot) {
        // Create realistic parking garage images based on the parking spot
        let images = [
            "parking_garage_1", "parking_garage_2", "parking_garage_3",
            "parking_garage_4", "parking_garage_5", "parking_garage_6"
        ]

        // Use a consistent image based on parking spot ID
        let imageIndex = abs(parkingSpot.id.hashValue) % images.count
        let imageName = images[imageIndex]

        // Try to load the image from bundle, fallback to generated image
        if let bundleImage = UIImage(named: imageName) {
            self.image = bundleImage
        } else {
            // Generate a realistic parking garage placeholder
            let renderer = UIGraphicsImageRenderer(size: CGSize(width: 80, height: 60))
            let backgroundImage = renderer.image { context in
                // Create a gradient background
                let colors = [
                    UIColor(red: 0.9, green: 0.9, blue: 0.9, alpha: 1.0).cgColor,
                    UIColor(red: 0.8, green: 0.8, blue: 0.8, alpha: 1.0).cgColor
                ]

                let gradient = CGGradient(colorsSpace: CGColorSpaceCreateDeviceRGB(), colors: colors as CFArray, locations: nil)!
                context.cgContext.drawLinearGradient(gradient, start: CGPoint(x: 0, y: 0), end: CGPoint(x: 0, y: 60), options: [])

                // Draw parking structure lines
                UIColor(red: 0.7, green: 0.7, blue: 0.7, alpha: 1.0).setStroke()
                context.cgContext.setLineWidth(1.0)

                // Horizontal lines (floors)
                for i in 1..<4 {
                    let y = CGFloat(i) * 15
                    context.cgContext.move(to: CGPoint(x: 10, y: y))
                    context.cgContext.addLine(to: CGPoint(x: 70, y: y))
                    context.cgContext.strokePath()
                }

                // Vertical lines (columns)
                for i in 1..<4 {
                    let x = CGFloat(i) * 20 + 10
                    context.cgContext.move(to: CGPoint(x: x, y: 5))
                    context.cgContext.addLine(to: CGPoint(x: x, y: 55))
                    context.cgContext.strokePath()
                }

                // Draw some cars
                UIColor.systemBlue.withAlphaComponent(0.6).setFill()
                let carRect1 = CGRect(x: 15, y: 20, width: 12, height: 8)
                context.cgContext.fillEllipse(in: carRect1)

                UIColor.systemRed.withAlphaComponent(0.6).setFill()
                let carRect2 = CGRect(x: 35, y: 35, width: 12, height: 8)
                context.cgContext.fillEllipse(in: carRect2)

                UIColor.systemGreen.withAlphaComponent(0.6).setFill()
                let carRect3 = CGRect(x: 55, y: 10, width: 12, height: 8)
                context.cgContext.fillEllipse(in: carRect3)
            }

            self.image = backgroundImage
        }

        self.contentMode = .scaleAspectFill
        self.clipsToBounds = true
        self.layer.cornerRadius = 8
    }
}

// MARK: - Image Loading Helper
extension UIImageView {
    func loadImage(from urlString: String?, placeholder: UIImage? = nil) {
        // Set placeholder first
        self.image = placeholder
        
        guard let urlString = urlString,
              let url = URL(string: urlString) else {
            return
        }
        
        // Simple image loading - in a real app, you'd use a proper image loading library
        URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            guard let data = data,
                  let image = UIImage(data: data),
                  error == nil else {
                return
            }
            
            DispatchQueue.main.async {
                self?.image = image
            }
        }.resume()
    }
}
