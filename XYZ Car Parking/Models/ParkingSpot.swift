//
//  ParkingSpot.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import Foundation
import CoreLocation

struct ParkingSpot {
    let id: String
    let name: String
    let address: String
    let pricePerHour: Double
    let availableSpots: Int
    let coordinate: CLLocationCoordinate2D
    let imageURL: String?
    
    init(id: String = UUID().uuidString, 
         name: String, 
         address: String, 
         pricePerHour: Double, 
         availableSpots: Int, 
         coordinate: CLLocationCoordinate2D, 
         imageURL: String? = nil) {
        self.id = id
        self.name = name
        self.address = address
        self.pricePerHour = pricePerHour
        self.availableSpots = availableSpots
        self.coordinate = coordinate
        self.imageURL = imageURL
    }
}

// MARK: - Sample Data
extension ParkingSpot {
    static let sampleData: [ParkingSpot] = [
        ParkingSpot(
            name: "Collins Street Car Park",
            address: "123 Collins Street, Melbourne VIC 3000",
            pricePerHour: 8.50,
            availableSpots: 45,
            coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631),
            imageURL: "parking_1"
        ),
        ParkingSpot(
            name: "Flinders Lane Parking",
            address: "67 Flinders Lane, Melbourne VIC 3000",
            pricePerHour: 12.00,
            availableSpots: 23,
            coordinate: CLLocationCoordinate2D(latitude: -37.8162, longitude: 144.9692),
            imageURL: "parking_2"
        ),
        ParkingSpot(
            name: "Queen Victoria Market Parking",
            address: "Queen Victoria Market, Melbourne VIC 3000",
            pricePerHour: 6.00,
            availableSpots: 78,
            coordinate: CLLocationCoordinate2D(latitude: -37.8076, longitude: 144.9568),
            imageURL: "parking_3"
        ),
        ParkingSpot(
            name: "Southern Cross Station Parking",
            address: "Southern Cross Station, Melbourne VIC 3000",
            pricePerHour: 15.00,
            availableSpots: 12,
            coordinate: CLLocationCoordinate2D(latitude: -37.8183, longitude: 144.9528),
            imageURL: "parking_4"
        ),
        ParkingSpot(
            name: "Federation Square Parking",
            address: "Federation Square, Melbourne VIC 3000",
            pricePerHour: 10.50,
            availableSpots: 34,
            coordinate: CLLocationCoordinate2D(latitude: -37.8179, longitude: 144.9690),
            imageURL: "parking_5"
        ),
        ParkingSpot(
            name: "Melbourne Central Parking",
            address: "Melbourne Central, Melbourne VIC 3000",
            pricePerHour: 14.00,
            availableSpots: 56,
            coordinate: CLLocationCoordinate2D(latitude: -37.8103, longitude: 144.9633),
            imageURL: "parking_6"
        ),
        ParkingSpot(
            name: "Bourke Street Mall Parking",
            address: "Bourke Street Mall, Melbourne VIC 3000",
            pricePerHour: 11.00,
            availableSpots: 19,
            coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9651),
            imageURL: "parking_7"
        ),
        ParkingSpot(
            name: "Docklands Stadium Parking",
            address: "Docklands Stadium, Melbourne VIC 3008",
            pricePerHour: 7.50,
            availableSpots: 89,
            coordinate: CLLocationCoordinate2D(latitude: -37.8164, longitude: 144.9475),
            imageURL: "parking_8"
        )
    ]
}
