//
//  ParkingSpot.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import Foundation
import CoreLocation

struct ParkingSpot {
    let id: String
    let name: String
    let address: String
    let pricePerHour: Double
    let availableSpots: Int
    let coordinate: CLLocationCoordinate2D
    let imageURL: String?
    let rating: Double
    let reviewCount: Int
    let openingHours: String
    let category: String

    init(id: String = UUID().uuidString,
         name: String,
         address: String,
         pricePerHour: Double,
         availableSpots: Int,
         coordinate: CLLocationCoordinate2D,
         imageURL: String? = nil,
         rating: Double = 4.5,
         reviewCount: Int = 100,
         openingHours: String = "Open 24 hours",
         category: String = "Parking Structure") {
        self.id = id
        self.name = name
        self.address = address
        self.pricePerHour = pricePerHour
        self.availableSpots = availableSpots
        self.coordinate = coordinate
        self.imageURL = imageURL
        self.rating = rating
        self.reviewCount = reviewCount
        self.openingHours = openingHours
        self.category = category
    }
}

// MARK: - Sample Data
extension ParkingSpot {
    static let sampleData: [ParkingSpot] = [
        ParkingSpot(
            name: "La Jolla Parking Structure",
            address: "Chesapeake Avenue",
            pricePerHour: 10.0,
            availableSpots: 24,
            coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9631),
            imageURL: "parking_1",
            rating: 4.5,
            reviewCount: 274,
            openingHours: "Open • Closes at midnight",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "La Jolla Parking Structure",
            address: "Chesapeake Avenue",
            pricePerHour: 15.0,
            availableSpots: 24,
            coordinate: CLLocationCoordinate2D(latitude: -37.8162, longitude: 144.9692),
            imageURL: "parking_2",
            rating: 4.3,
            reviewCount: 189,
            openingHours: "Open • Closes at midnight",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "La Jolla Parking Structure",
            address: "Chesapeake Avenue",
            pricePerHour: 8.0,
            availableSpots: 24,
            coordinate: CLLocationCoordinate2D(latitude: -37.8076, longitude: 144.9568),
            imageURL: "parking_3",
            rating: 4.7,
            reviewCount: 156,
            openingHours: "Open • Closes at midnight",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "Collins Street Car Park",
            address: "123 Collins Street, Melbourne VIC 3000",
            pricePerHour: 8.50,
            availableSpots: 45,
            coordinate: CLLocationCoordinate2D(latitude: -37.8183, longitude: 144.9528),
            imageURL: "parking_4",
            rating: 4.2,
            reviewCount: 98,
            openingHours: "Open 24 hours",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "Federation Square Parking",
            address: "Federation Square, Melbourne VIC 3000",
            pricePerHour: 10.50,
            availableSpots: 34,
            coordinate: CLLocationCoordinate2D(latitude: -37.8179, longitude: 144.9690),
            imageURL: "parking_5",
            rating: 4.1,
            reviewCount: 203,
            openingHours: "Open 24 hours",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "Melbourne Central Parking",
            address: "Melbourne Central, Melbourne VIC 3000",
            pricePerHour: 14.00,
            availableSpots: 56,
            coordinate: CLLocationCoordinate2D(latitude: -37.8103, longitude: 144.9633),
            imageURL: "parking_6",
            rating: 4.6,
            reviewCount: 312,
            openingHours: "Open 24 hours",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "Bourke Street Mall Parking",
            address: "Bourke Street Mall, Melbourne VIC 3000",
            pricePerHour: 11.00,
            availableSpots: 19,
            coordinate: CLLocationCoordinate2D(latitude: -37.8136, longitude: 144.9651),
            imageURL: "parking_7",
            rating: 4.0,
            reviewCount: 87,
            openingHours: "Open 24 hours",
            category: "Parking Structure"
        ),
        ParkingSpot(
            name: "Docklands Stadium Parking",
            address: "Docklands Stadium, Melbourne VIC 3008",
            pricePerHour: 7.50,
            availableSpots: 89,
            coordinate: CLLocationCoordinate2D(latitude: -37.8164, longitude: 144.9475),
            imageURL: "parking_8",
            rating: 4.4,
            reviewCount: 145,
            openingHours: "Open 24 hours",
            category: "Parking Structure"
        )
    ]
}
