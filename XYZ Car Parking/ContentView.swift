//
//  ContentView.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import SwiftUI
import UIKit

struct ContentView: View {
    var body: some View {
        MainViewControllerRepresentable()
            .ignoresSafeArea()
    }
}

struct MainViewControllerRepresentable: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> MainViewController {
        return MainViewController()
    }

    func updateUIViewController(_ uiViewController: MainViewController, context: Context) {
        // Update the view controller if needed
    }
}

#Preview {
    ContentView()
}
