//
//  TransportModeSelector.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

enum TransportMode: String, CaseIterable {
    case car = "Car"
    case bike = "Bike"
    case van = "Van"
    case more = "More"
    
    var icon: String {
        switch self {
        case .car:
            return "car.fill"
        case .bike:
            return "bicycle"
        case .van:
            return "truck.box.fill"
        case .more:
            return "ellipsis"
        }
    }
    
    var parkingTitle: String {
        switch self {
        case .car:
            return "Car Parking Nearby"
        case .bike:
            return "Bike Parking Nearby"
        case .van:
            return "Van Parking Nearby"
        case .more:
            return "Other Parking Nearby"
        }
    }
}

protocol TransportModeSelectorDelegate: AnyObject {
    func transportModeSelector(_ selector: TransportModeSelector, didSelectMode mode: TransportMode)
}

class TransportModeSelector: UIView {
    weak var delegate: TransportModeSelectorDelegate?
    private var selectedMode: TransportMode = .car
    private var modeButtons: [UIButton] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = UIColor.systemBackground
        
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        for mode in TransportMode.allCases {
            let button = createModeButton(for: mode)
            modeButtons.append(button)
            stackView.addArrangedSubview(button)
        }
        
        addSubview(stackView)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: topAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor),
            stackView.heightAnchor.constraint(equalToConstant: 60)
        ])
        
        // Select car by default
        updateSelection(for: .car)
    }
    
    private func createModeButton(for mode: TransportMode) -> UIButton {
        let button = UIButton(type: .system)
        button.tag = TransportMode.allCases.firstIndex(of: mode) ?? 0

        // Use modern UIButton configuration for iOS 15+
        var config = UIButton.Configuration.plain()
        config.imagePlacement = .top
        config.imagePadding = 4
        config.titleTextAttributesTransformer = UIConfigurationTextAttributesTransformer { incoming in
            var outgoing = incoming
            outgoing.font = UIFont.systemFont(ofSize: 12, weight: .medium)
            return outgoing
        }

        let symbolConfig = UIImage.SymbolConfiguration(pointSize: 24, weight: .medium)
        let image = UIImage(systemName: mode.icon, withConfiguration: symbolConfig)

        config.image = image
        config.title = mode.rawValue

        button.configuration = config
        button.addTarget(self, action: #selector(modeButtonTapped(_:)), for: .touchUpInside)

        return button
    }
    
    @objc private func modeButtonTapped(_ sender: UIButton) {
        let mode = TransportMode.allCases[sender.tag]
        selectedMode = mode
        updateSelection(for: mode)
        delegate?.transportModeSelector(self, didSelectMode: mode)
    }
    
    private func updateSelection(for mode: TransportMode) {
        for (index, button) in modeButtons.enumerated() {
            let isSelected = TransportMode.allCases[index] == mode
            button.tintColor = isSelected ? UIColor.systemBlue : UIColor.systemGray
            button.backgroundColor = isSelected ? UIColor.systemBlue.withAlphaComponent(0.1) : UIColor.clear
        }
    }
    
    func getSelectedMode() -> TransportMode {
        return selectedMode
    }
}
