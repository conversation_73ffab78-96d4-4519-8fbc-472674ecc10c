//
//  TransportModeSelector.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

enum TransportMode: String, CaseIterable {
    case car = "Car"
    case bike = "Bike"
    case van = "Van"
    case more = "More"
    
    var icon: String {
        switch self {
        case .car:
            return "car.fill"
        case .bike:
            return "bicycle"
        case .van:
            return "truck.box.fill"
        case .more:
            return "ellipsis"
        }
    }
    
    var parkingTitle: String {
        switch self {
        case .car:
            return "Car Parking Nearby"
        case .bike:
            return "Bike Parking Nearby"
        case .van:
            return "Van Parking Nearby"
        case .more:
            return "Other Parking Nearby"
        }
    }
}

protocol TransportModeSelectorDelegate: AnyObject {
    func transportModeSelector(_ selector: TransportModeSelector, didSelectMode mode: TransportMode)
}

class TransportModeSelector: UIView {
    weak var delegate: TransportModeSelectorDelegate?
    private var selectedMode: TransportMode = .car
    private var modeButtons: [UIButton] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = UIColor.systemBackground
        
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        for mode in TransportMode.allCases {
            let button = createModeButton(for: mode)
            modeButtons.append(button)
            stackView.addArrangedSubview(button)
        }
        
        addSubview(stackView)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: topAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor),
            stackView.heightAnchor.constraint(equalToConstant: 60)
        ])
        
        // Select car by default
        updateSelection(for: .car)
    }
    
    private func createModeButton(for mode: TransportMode) -> UIButton {
        let button = UIButton(type: .system)
        button.tag = TransportMode.allCases.firstIndex(of: mode) ?? 0

        // Create a container view for better layout control
        let containerView = UIView()
        containerView.translatesAutoresizingMaskIntoConstraints = false

        // Create icon view
        let iconView = UIView()
        iconView.backgroundColor = UIColor.systemGray6
        iconView.layer.cornerRadius = 20
        iconView.translatesAutoresizingMaskIntoConstraints = false

        let symbolConfig = UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        let imageView = UIImageView(image: UIImage(systemName: mode.icon, withConfiguration: symbolConfig))
        imageView.tintColor = UIColor.systemGray
        imageView.contentMode = .scaleAspectFit
        imageView.translatesAutoresizingMaskIntoConstraints = false

        iconView.addSubview(imageView)

        // Create label
        let label = UILabel()
        label.text = mode.rawValue
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor.systemGray
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false

        containerView.addSubview(iconView)
        containerView.addSubview(label)
        button.addSubview(containerView)

        NSLayoutConstraint.activate([
            // Container
            containerView.centerXAnchor.constraint(equalTo: button.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: button.centerYAnchor),

            // Icon view
            iconView.topAnchor.constraint(equalTo: containerView.topAnchor),
            iconView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            iconView.widthAnchor.constraint(equalToConstant: 40),
            iconView.heightAnchor.constraint(equalToConstant: 40),

            // Image view
            imageView.centerXAnchor.constraint(equalTo: iconView.centerXAnchor),
            imageView.centerYAnchor.constraint(equalTo: iconView.centerYAnchor),
            imageView.widthAnchor.constraint(equalToConstant: 20),
            imageView.heightAnchor.constraint(equalToConstant: 20),

            // Label
            label.topAnchor.constraint(equalTo: iconView.bottomAnchor, constant: 4),
            label.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            label.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
        ])

        button.addTarget(self, action: #selector(modeButtonTapped(_:)), for: .touchUpInside)

        return button
    }
    
    @objc private func modeButtonTapped(_ sender: UIButton) {
        let mode = TransportMode.allCases[sender.tag]
        selectedMode = mode
        updateSelection(for: mode)
        delegate?.transportModeSelector(self, didSelectMode: mode)
    }
    
    private func updateSelection(for mode: TransportMode) {
        for (index, button) in modeButtons.enumerated() {
            let isSelected = TransportMode.allCases[index] == mode

            // Find the icon view and label in the button
            if let containerView = button.subviews.first,
               let iconView = containerView.subviews.first(where: { $0.backgroundColor != nil }),
               let imageView = iconView.subviews.first as? UIImageView,
               let label = containerView.subviews.first(where: { $0 is UILabel }) as? UILabel {

                if isSelected {
                    iconView.backgroundColor = UIColor.systemBlue
                    imageView.tintColor = UIColor.white
                    label.textColor = UIColor.systemBlue
                } else {
                    iconView.backgroundColor = UIColor.systemGray6
                    imageView.tintColor = UIColor.systemGray
                    label.textColor = UIColor.systemGray
                }
            }
        }
    }
    
    func getSelectedMode() -> TransportMode {
        return selectedMode
    }
}
