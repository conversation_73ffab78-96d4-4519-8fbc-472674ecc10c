//
//  ParkingListCell.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

class ParkingListCell: UITableViewCell {
    static let identifier = "ParkingListCell"
    
    private let parkingImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = UIColor.systemGray5
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let addressLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.secondaryLabel
        label.numberOfLines = 2
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let locationIconImageView: UIImageView = {
        let imageView = UIImageView()
        let config = UIImage.SymbolConfiguration(pointSize: 12, weight: .medium)
        imageView.image = UIImage(systemName: "location.fill", withConfiguration: config)
        imageView.tintColor = UIColor.systemGray
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    private let priceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = UIColor.systemBlue
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let spotsLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.systemGray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let ratingStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 2
        stackView.alignment = .center
        stackView.translatesAutoresizingMaskIntoConstraints = false
        return stackView
    }()

    private let ratingLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.textColor = UIColor.systemGray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let categoryLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor.systemGray2
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let hoursLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        label.textColor = UIColor.systemGreen
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        contentView.addSubview(parkingImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(locationIconImageView)
        contentView.addSubview(addressLabel)
        contentView.addSubview(priceLabel)
        contentView.addSubview(spotsLabel)
        contentView.addSubview(ratingStackView)
        contentView.addSubview(categoryLabel)
        contentView.addSubview(hoursLabel)

        setupRatingStars()
        
        NSLayoutConstraint.activate([
            // Parking image
            parkingImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            parkingImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            parkingImageView.widthAnchor.constraint(equalToConstant: 80),
            parkingImageView.heightAnchor.constraint(equalToConstant: 60),

            // Name label
            nameLabel.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            nameLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            nameLabel.trailingAnchor.constraint(equalTo: priceLabel.leadingAnchor, constant: -8),

            // Rating stack view
            ratingStackView.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            ratingStackView.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),

            // Category label
            categoryLabel.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            categoryLabel.topAnchor.constraint(equalTo: ratingStackView.bottomAnchor, constant: 2),

            // Location icon
            locationIconImageView.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            locationIconImageView.topAnchor.constraint(equalTo: categoryLabel.bottomAnchor, constant: 4),
            locationIconImageView.widthAnchor.constraint(equalToConstant: 12),
            locationIconImageView.heightAnchor.constraint(equalToConstant: 12),

            // Address label
            addressLabel.leadingAnchor.constraint(equalTo: locationIconImageView.trailingAnchor, constant: 4),
            addressLabel.topAnchor.constraint(equalTo: categoryLabel.bottomAnchor, constant: 2),
            addressLabel.trailingAnchor.constraint(equalTo: priceLabel.leadingAnchor, constant: -8),

            // Hours label
            hoursLabel.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            hoursLabel.topAnchor.constraint(equalTo: addressLabel.bottomAnchor, constant: 2),
            hoursLabel.bottomAnchor.constraint(lessThanOrEqualTo: contentView.bottomAnchor, constant: -12),

            // Price label
            priceLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            priceLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            priceLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 60),

            // Spots label
            spotsLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            spotsLabel.topAnchor.constraint(equalTo: priceLabel.bottomAnchor, constant: 4),

            // Cell height constraint
            contentView.heightAnchor.constraint(greaterThanOrEqualToConstant: 110)
        ])
        
        selectionStyle = .none
    }

    private func setupRatingStars() {
        // Clear existing stars
        ratingStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add 5 star images
        for i in 0..<5 {
            let starImageView = UIImageView()
            let config = UIImage.SymbolConfiguration(pointSize: 10, weight: .medium)
            starImageView.image = UIImage(systemName: "star.fill", withConfiguration: config)
            starImageView.tintColor = UIColor.systemGray4
            starImageView.translatesAutoresizingMaskIntoConstraints = false
            starImageView.widthAnchor.constraint(equalToConstant: 10).isActive = true
            starImageView.heightAnchor.constraint(equalToConstant: 10).isActive = true
            ratingStackView.addArrangedSubview(starImageView)
        }

        // Add rating label
        ratingStackView.addArrangedSubview(ratingLabel)
    }

    private func updateRatingStars(rating: Double, reviewCount: Int) {
        let fullStars = Int(rating)
        let hasHalfStar = rating - Double(fullStars) >= 0.5

        for (index, view) in ratingStackView.arrangedSubviews.enumerated() {
            guard let starImageView = view as? UIImageView, index < 5 else { continue }

            let config = UIImage.SymbolConfiguration(pointSize: 10, weight: .medium)
            if index < fullStars {
                starImageView.image = UIImage(systemName: "star.fill", withConfiguration: config)
                starImageView.tintColor = UIColor.systemOrange
            } else if index == fullStars && hasHalfStar {
                starImageView.image = UIImage(systemName: "star.leadinghalf.filled", withConfiguration: config)
                starImageView.tintColor = UIColor.systemOrange
            } else {
                starImageView.image = UIImage(systemName: "star.fill", withConfiguration: config)
                starImageView.tintColor = UIColor.systemGray4
            }
        }

        ratingLabel.text = String(format: "%.1f (%d)", rating, reviewCount)
    }

    func configure(with parkingSpot: ParkingSpot) {
        nameLabel.text = parkingSpot.name
        addressLabel.text = parkingSpot.address
        priceLabel.text = String(format: "$%.0f/hr", parkingSpot.pricePerHour)
        spotsLabel.text = "\(parkingSpot.availableSpots) spots"
        categoryLabel.text = parkingSpot.category
        hoursLabel.text = parkingSpot.openingHours

        updateRatingStars(rating: parkingSpot.rating, reviewCount: parkingSpot.reviewCount)

        // Set placeholder image with color coding based on availability
        parkingImageView.setPlaceholderImage(for: parkingSpot)

        // If there's an image URL, load it (for future use)
        if let imageURL = parkingSpot.imageURL {
            parkingImageView.loadImage(from: imageURL, placeholder: parkingImageView.image)
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        parkingImageView.image = nil
        nameLabel.text = nil
        addressLabel.text = nil
        priceLabel.text = nil
        spotsLabel.text = nil
        categoryLabel.text = nil
        hoursLabel.text = nil
        ratingLabel.text = nil
    }
}
