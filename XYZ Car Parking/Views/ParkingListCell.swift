//
//  ParkingListCell.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

class ParkingListCell: UITableViewCell {
    static let identifier = "ParkingListCell"
    
    private let parkingImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = UIColor.systemGray5
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    private let nameLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let addressLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.secondaryLabel
        label.numberOfLines = 2
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let locationIconImageView: UIImageView = {
        let imageView = UIImageView()
        let config = UIImage.SymbolConfiguration(pointSize: 12, weight: .medium)
        imageView.image = UIImage(systemName: "location.fill", withConfiguration: config)
        imageView.tintColor = UIColor.systemGray
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()
    
    private let priceLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        label.textColor = UIColor.systemBlue
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let spotsLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.systemGray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        contentView.addSubview(parkingImageView)
        contentView.addSubview(nameLabel)
        contentView.addSubview(locationIconImageView)
        contentView.addSubview(addressLabel)
        contentView.addSubview(priceLabel)
        contentView.addSubview(spotsLabel)
        
        NSLayoutConstraint.activate([
            // Parking image
            parkingImageView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            parkingImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            parkingImageView.widthAnchor.constraint(equalToConstant: 80),
            parkingImageView.heightAnchor.constraint(equalToConstant: 60),
            
            // Name label
            nameLabel.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            nameLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            nameLabel.trailingAnchor.constraint(equalTo: priceLabel.leadingAnchor, constant: -8),
            
            // Location icon
            locationIconImageView.leadingAnchor.constraint(equalTo: parkingImageView.trailingAnchor, constant: 12),
            locationIconImageView.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 4),
            locationIconImageView.widthAnchor.constraint(equalToConstant: 12),
            locationIconImageView.heightAnchor.constraint(equalToConstant: 12),
            
            // Address label
            addressLabel.leadingAnchor.constraint(equalTo: locationIconImageView.trailingAnchor, constant: 4),
            addressLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 2),
            addressLabel.trailingAnchor.constraint(equalTo: priceLabel.leadingAnchor, constant: -8),
            
            // Price label
            priceLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            priceLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 12),
            priceLabel.widthAnchor.constraint(greaterThanOrEqualToConstant: 60),
            
            // Spots label
            spotsLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            spotsLabel.topAnchor.constraint(equalTo: priceLabel.bottomAnchor, constant: 4),
            spotsLabel.bottomAnchor.constraint(lessThanOrEqualTo: contentView.bottomAnchor, constant: -12),
            
            // Cell height constraint
            contentView.heightAnchor.constraint(greaterThanOrEqualToConstant: 84)
        ])
        
        selectionStyle = .none
    }
    
    func configure(with parkingSpot: ParkingSpot) {
        nameLabel.text = parkingSpot.name
        addressLabel.text = parkingSpot.address
        priceLabel.text = String(format: "$%.2f/hr", parkingSpot.pricePerHour)
        spotsLabel.text = "\(parkingSpot.availableSpots) spots"

        // Set placeholder image with color coding based on availability
        parkingImageView.setPlaceholderImage(for: parkingSpot)

        // If there's an image URL, load it (for future use)
        if let imageURL = parkingSpot.imageURL {
            parkingImageView.loadImage(from: imageURL, placeholder: parkingImageView.image)
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        parkingImageView.image = nil
        nameLabel.text = nil
        addressLabel.text = nil
        priceLabel.text = nil
        spotsLabel.text = nil
    }
}
