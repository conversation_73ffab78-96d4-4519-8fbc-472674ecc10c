//
//  BottomSheetViewController.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

protocol BottomSheetDelegate: AnyObject {
    func bottomSheetDidChangeState(_ bottomSheet: BottomSheetViewController, state: BottomSheetState)
    func bottomSheetDidSelectParking(_ bottomSheet: BottomSheetViewController, parkingSpot: ParkingSpot)
}

enum BottomSheetState {
    case collapsed
    case expanded
}

class BottomSheetViewController: UIViewController {
    weak var delegate: BottomSheetDelegate?
    
    private var currentState: BottomSheetState = .collapsed
    private var parkingSpots: [ParkingSpot] = []
    private var selectedTransportMode: TransportMode = .car
    
    // UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBackground
        view.layer.cornerRadius = 16
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: -2)
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 8
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private let handleView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemGray4
        view.layer.cornerRadius = 2
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private let searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "La Jolla Parking Struct"
        searchBar.searchBarStyle = .minimal
        searchBar.layer.cornerRadius = 22
        searchBar.layer.masksToBounds = true
        searchBar.backgroundColor = UIColor.systemGray6
        searchBar.translatesAutoresizingMaskIntoConstraints = false
        return searchBar
    }()

    // Collapsed state card view
    private let collapsedCardView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBackground
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 8
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private let collapsedImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = UIColor.systemGray5
        imageView.translatesAutoresizingMaskIntoConstraints = false
        return imageView
    }()

    private let collapsedNameLabel: UILabel = {
        let label = UILabel()
        label.text = "La Jolla Parking Structure"
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let collapsedRatingStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 2
        stackView.alignment = .center
        stackView.translatesAutoresizingMaskIntoConstraints = false
        return stackView
    }()

    private let collapsedCategoryLabel: UILabel = {
        let label = UILabel()
        label.text = "Parking Structure"
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.systemGray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let collapsedPriceLabel: UILabel = {
        let label = UILabel()
        label.text = "$3 per hour"
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let collapsedSpotsLabel: UILabel = {
        let label = UILabel()
        label.text = "24 spots available"
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor.systemGray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let navigateButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Navigate To Here", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor.systemBlue
        button.layer.cornerRadius = 22
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    private let microphoneButton: UIButton = {
        let button = UIButton(type: .system)
        let config = UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        button.setImage(UIImage(systemName: "mic.fill", withConfiguration: config), for: .normal)
        button.tintColor = UIColor.white
        button.backgroundColor = UIColor.systemBlue
        button.layer.cornerRadius = 22
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    private let transportModeSelector: TransportModeSelector = {
        let selector = TransportModeSelector()
        selector.translatesAutoresizingMaskIntoConstraints = false
        return selector
    }()
    
    private let headerLabel: UILabel = {
        let label = UILabel()
        label.text = "Car Parking Nearby"
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = UIColor.systemBackground
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.translatesAutoresizingMaskIntoConstraints = false
        return tableView
    }()
    
    // Constraints
    private var containerBottomConstraint: NSLayoutConstraint!
    private var collapsedHeight: CGFloat = 280
    private var expandedHeight: CGFloat = 500
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
        setupGestures()
        setupTableView()
        setupDelegates()
    }
    
    private func setupView() {
        view.addSubview(containerView)
        containerView.addSubview(handleView)
        containerView.addSubview(searchBar)
        containerView.addSubview(microphoneButton)
        containerView.addSubview(collapsedCardView)
        containerView.addSubview(transportModeSelector)
        containerView.addSubview(headerLabel)
        containerView.addSubview(tableView)

        // Setup collapsed card content
        collapsedCardView.addSubview(collapsedImageView)
        collapsedCardView.addSubview(collapsedNameLabel)
        collapsedCardView.addSubview(collapsedRatingStackView)
        collapsedCardView.addSubview(collapsedCategoryLabel)
        collapsedCardView.addSubview(collapsedPriceLabel)
        collapsedCardView.addSubview(collapsedSpotsLabel)
        collapsedCardView.addSubview(navigateButton)

        setupCollapsedRatingStars()
        
        containerBottomConstraint = containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        
        NSLayoutConstraint.activate([
            // Container
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            containerBottomConstraint,
            containerView.heightAnchor.constraint(equalToConstant: collapsedHeight),
            
            // Handle
            handleView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            handleView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            handleView.widthAnchor.constraint(equalToConstant: 40),
            handleView.heightAnchor.constraint(equalToConstant: 4),
            
            // Search bar
            searchBar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            searchBar.topAnchor.constraint(equalTo: handleView.bottomAnchor, constant: 8),
            searchBar.trailingAnchor.constraint(equalTo: microphoneButton.leadingAnchor, constant: -8),
            searchBar.heightAnchor.constraint(equalToConstant: 44),
            
            // Microphone button
            microphoneButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            microphoneButton.centerYAnchor.constraint(equalTo: searchBar.centerYAnchor),
            microphoneButton.widthAnchor.constraint(equalToConstant: 44),
            microphoneButton.heightAnchor.constraint(equalToConstant: 44),

            // Collapsed card view
            collapsedCardView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            collapsedCardView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            collapsedCardView.topAnchor.constraint(equalTo: searchBar.bottomAnchor, constant: 16),
            collapsedCardView.heightAnchor.constraint(equalToConstant: 200),

            // Transport mode selector
            transportModeSelector.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            transportModeSelector.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            transportModeSelector.topAnchor.constraint(equalTo: searchBar.bottomAnchor, constant: 8),
            
            // Header label
            headerLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            headerLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            headerLabel.topAnchor.constraint(equalTo: transportModeSelector.bottomAnchor, constant: 16),
            
            // Table view
            tableView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            tableView.topAnchor.constraint(equalTo: headerLabel.bottomAnchor, constant: 8),
            tableView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -view.safeAreaInsets.bottom)
        ])

        // Setup collapsed card constraints
        setupCollapsedCardConstraints()

        // Initially hide expanded content
        transportModeSelector.alpha = 0
        headerLabel.alpha = 0
        tableView.alpha = 0
        collapsedCardView.alpha = 1
    }

    private func setupCollapsedCardConstraints() {
        NSLayoutConstraint.activate([
            // Collapsed image
            collapsedImageView.leadingAnchor.constraint(equalTo: collapsedCardView.leadingAnchor, constant: 16),
            collapsedImageView.topAnchor.constraint(equalTo: collapsedCardView.topAnchor, constant: 16),
            collapsedImageView.widthAnchor.constraint(equalToConstant: 80),
            collapsedImageView.heightAnchor.constraint(equalToConstant: 60),

            // Collapsed name label
            collapsedNameLabel.leadingAnchor.constraint(equalTo: collapsedImageView.trailingAnchor, constant: 12),
            collapsedNameLabel.topAnchor.constraint(equalTo: collapsedCardView.topAnchor, constant: 16),
            collapsedNameLabel.trailingAnchor.constraint(equalTo: collapsedCardView.trailingAnchor, constant: -16),

            // Collapsed rating stack view
            collapsedRatingStackView.leadingAnchor.constraint(equalTo: collapsedImageView.trailingAnchor, constant: 12),
            collapsedRatingStackView.topAnchor.constraint(equalTo: collapsedNameLabel.bottomAnchor, constant: 4),

            // Collapsed category label
            collapsedCategoryLabel.leadingAnchor.constraint(equalTo: collapsedImageView.trailingAnchor, constant: 12),
            collapsedCategoryLabel.topAnchor.constraint(equalTo: collapsedRatingStackView.bottomAnchor, constant: 4),

            // Collapsed price label
            collapsedPriceLabel.leadingAnchor.constraint(equalTo: collapsedCardView.leadingAnchor, constant: 16),
            collapsedPriceLabel.topAnchor.constraint(equalTo: collapsedImageView.bottomAnchor, constant: 12),

            // Collapsed spots label
            collapsedSpotsLabel.trailingAnchor.constraint(equalTo: collapsedCardView.trailingAnchor, constant: -16),
            collapsedSpotsLabel.centerYAnchor.constraint(equalTo: collapsedPriceLabel.centerYAnchor),

            // Navigate button
            navigateButton.leadingAnchor.constraint(equalTo: collapsedCardView.leadingAnchor, constant: 16),
            navigateButton.trailingAnchor.constraint(equalTo: collapsedCardView.trailingAnchor, constant: -16),
            navigateButton.topAnchor.constraint(equalTo: collapsedPriceLabel.bottomAnchor, constant: 16),
            navigateButton.heightAnchor.constraint(equalToConstant: 44),
            navigateButton.bottomAnchor.constraint(equalTo: collapsedCardView.bottomAnchor, constant: -16)
        ])
    }

    private func setupCollapsedRatingStars() {
        // Clear existing stars
        collapsedRatingStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // Add 5 star images
        for i in 0..<5 {
            let starImageView = UIImageView()
            let config = UIImage.SymbolConfiguration(pointSize: 12, weight: .medium)
            starImageView.image = UIImage(systemName: "star.fill", withConfiguration: config)
            starImageView.tintColor = i < 4 ? UIColor.systemOrange : UIColor.systemGray4
            starImageView.translatesAutoresizingMaskIntoConstraints = false
            starImageView.widthAnchor.constraint(equalToConstant: 12).isActive = true
            starImageView.heightAnchor.constraint(equalToConstant: 12).isActive = true
            collapsedRatingStackView.addArrangedSubview(starImageView)
        }

        // Add rating label
        let ratingLabel = UILabel()
        ratingLabel.text = "4.5 (274)"
        ratingLabel.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        ratingLabel.textColor = UIColor.systemGray
        ratingLabel.translatesAutoresizingMaskIntoConstraints = false
        collapsedRatingStackView.addArrangedSubview(ratingLabel)
    }

    private func setupGestures() {
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        containerView.addGestureRecognizer(panGesture)

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTapGesture(_:)))
        handleView.addGestureRecognizer(tapGesture)
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ParkingListCell.self, forCellReuseIdentifier: ParkingListCell.identifier)
    }

    private func setupDelegates() {
        transportModeSelector.delegate = self
        searchBar.delegate = self
    }

    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)

        switch gesture.state {
        case .changed:
            // Handle dragging
            break
        case .ended:
            if velocity.y < -500 || (translation.y < -50 && currentState == .collapsed) {
                expandBottomSheet()
            } else if velocity.y > 500 || (translation.y > 50 && currentState == .expanded) {
                collapseBottomSheet()
            } else {
                // Snap back to current state
                animateToState(currentState)
            }
        default:
            break
        }
    }

    @objc private func handleTapGesture(_ gesture: UITapGestureRecognizer) {
        if currentState == .collapsed {
            expandBottomSheet()
        } else {
            collapseBottomSheet()
        }
    }

    private func expandBottomSheet() {
        currentState = .expanded
        animateToState(.expanded)
        delegate?.bottomSheetDidChangeState(self, state: .expanded)
    }

    private func collapseBottomSheet() {
        currentState = .collapsed
        animateToState(.collapsed)
        delegate?.bottomSheetDidChangeState(self, state: .collapsed)
    }

    private func animateToState(_ state: BottomSheetState) {
        let height = state == .expanded ? expandedHeight : collapsedHeight
        let expandedAlpha: CGFloat = state == .expanded ? 1.0 : 0.0
        let collapsedAlpha: CGFloat = state == .collapsed ? 1.0 : 0.0

        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseInOut) {
            self.containerView.constraints.first { $0.firstAttribute == .height }?.constant = height
            self.transportModeSelector.alpha = expandedAlpha
            self.headerLabel.alpha = expandedAlpha
            self.tableView.alpha = expandedAlpha
            self.collapsedCardView.alpha = collapsedAlpha
            self.view.layoutIfNeeded()
        }
    }

    func updateParkingSpots(_ spots: [ParkingSpot]) {
        parkingSpots = spots
        tableView.reloadData()
    }
}

// MARK: - UITableViewDataSource
extension BottomSheetViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return parkingSpots.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: ParkingListCell.identifier, for: indexPath) as? ParkingListCell else {
            return UITableViewCell()
        }

        let parkingSpot = parkingSpots[indexPath.row]
        cell.configure(with: parkingSpot)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension BottomSheetViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let parkingSpot = parkingSpots[indexPath.row]
        delegate?.bottomSheetDidSelectParking(self, parkingSpot: parkingSpot)
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 110
    }
}

// MARK: - TransportModeSelectorDelegate
extension BottomSheetViewController: TransportModeSelectorDelegate {
    func transportModeSelector(_ selector: TransportModeSelector, didSelectMode mode: TransportMode) {
        selectedTransportMode = mode
        headerLabel.text = mode.parkingTitle
        // Here you could filter parking spots based on transport mode
    }
}

// MARK: - UISearchBarDelegate
extension BottomSheetViewController: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        // Implement search functionality
        if searchText.isEmpty {
            updateParkingSpots(ParkingSpot.sampleData)
        } else {
            let filteredSpots = ParkingSpot.sampleData.filter { spot in
                spot.name.localizedCaseInsensitiveContains(searchText) ||
                spot.address.localizedCaseInsensitiveContains(searchText)
            }
            updateParkingSpots(filteredSpots)
        }
    }

    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}
