//
//  BottomSheetViewController.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import UIKit

protocol BottomSheetDelegate: AnyObject {
    func bottomSheetDidChangeState(_ bottomSheet: BottomSheetViewController, state: BottomSheetState)
    func bottomSheetDidSelectParking(_ bottomSheet: BottomSheetViewController, parkingSpot: ParkingSpot)
}

enum BottomSheetState {
    case collapsed
    case expanded
}

class BottomSheetViewController: UIViewController {
    weak var delegate: BottomSheetDelegate?
    
    private var currentState: BottomSheetState = .collapsed
    private var parkingSpots: [ParkingSpot] = []
    private var selectedTransportMode: TransportMode = .car
    
    // UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemBackground
        view.layer.cornerRadius = 16
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: -2)
        view.layer.shadowOpacity = 0.1
        view.layer.shadowRadius = 8
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private let handleView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemGray4
        view.layer.cornerRadius = 2
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()
    
    private let searchBar: UISearchBar = {
        let searchBar = UISearchBar()
        searchBar.placeholder = "Search parking spots..."
        searchBar.searchBarStyle = .minimal
        searchBar.translatesAutoresizingMaskIntoConstraints = false
        return searchBar
    }()
    
    private let microphoneButton: UIButton = {
        let button = UIButton(type: .system)
        let config = UIImage.SymbolConfiguration(pointSize: 20, weight: .medium)
        button.setImage(UIImage(systemName: "mic.fill", withConfiguration: config), for: .normal)
        button.tintColor = UIColor.systemBlue
        button.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.1)
        button.layer.cornerRadius = 20
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()
    
    private let transportModeSelector: TransportModeSelector = {
        let selector = TransportModeSelector()
        selector.translatesAutoresizingMaskIntoConstraints = false
        return selector
    }()
    
    private let headerLabel: UILabel = {
        let label = UILabel()
        label.text = "Car Parking Nearby"
        label.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        label.textColor = UIColor.label
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()
    
    private let tableView: UITableView = {
        let tableView = UITableView()
        tableView.backgroundColor = UIColor.systemBackground
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.translatesAutoresizingMaskIntoConstraints = false
        return tableView
    }()
    
    // Constraints
    private var containerBottomConstraint: NSLayoutConstraint!
    private var collapsedHeight: CGFloat = 120
    private var expandedHeight: CGFloat = 500
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupView()
        setupGestures()
        setupTableView()
        setupDelegates()
    }
    
    private func setupView() {
        view.addSubview(containerView)
        containerView.addSubview(handleView)
        containerView.addSubview(searchBar)
        containerView.addSubview(microphoneButton)
        containerView.addSubview(transportModeSelector)
        containerView.addSubview(headerLabel)
        containerView.addSubview(tableView)
        
        containerBottomConstraint = containerView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        
        NSLayoutConstraint.activate([
            // Container
            containerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            containerBottomConstraint,
            containerView.heightAnchor.constraint(equalToConstant: collapsedHeight),
            
            // Handle
            handleView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            handleView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 8),
            handleView.widthAnchor.constraint(equalToConstant: 40),
            handleView.heightAnchor.constraint(equalToConstant: 4),
            
            // Search bar
            searchBar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            searchBar.topAnchor.constraint(equalTo: handleView.bottomAnchor, constant: 8),
            searchBar.trailingAnchor.constraint(equalTo: microphoneButton.leadingAnchor, constant: -8),
            searchBar.heightAnchor.constraint(equalToConstant: 44),
            
            // Microphone button
            microphoneButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            microphoneButton.centerYAnchor.constraint(equalTo: searchBar.centerYAnchor),
            microphoneButton.widthAnchor.constraint(equalToConstant: 40),
            microphoneButton.heightAnchor.constraint(equalToConstant: 40),
            
            // Transport mode selector
            transportModeSelector.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            transportModeSelector.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            transportModeSelector.topAnchor.constraint(equalTo: searchBar.bottomAnchor, constant: 8),
            
            // Header label
            headerLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            headerLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),
            headerLabel.topAnchor.constraint(equalTo: transportModeSelector.bottomAnchor, constant: 16),
            
            // Table view
            tableView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            tableView.topAnchor.constraint(equalTo: headerLabel.bottomAnchor, constant: 8),
            tableView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -view.safeAreaInsets.bottom)
        ])
        
        // Initially hide expanded content
        transportModeSelector.alpha = 0
        headerLabel.alpha = 0
        tableView.alpha = 0
    }

    private func setupGestures() {
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        containerView.addGestureRecognizer(panGesture)

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTapGesture(_:)))
        handleView.addGestureRecognizer(tapGesture)
    }

    private func setupTableView() {
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ParkingListCell.self, forCellReuseIdentifier: ParkingListCell.identifier)
    }

    private func setupDelegates() {
        transportModeSelector.delegate = self
        searchBar.delegate = self
    }

    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)

        switch gesture.state {
        case .changed:
            // Handle dragging
            break
        case .ended:
            if velocity.y < -500 || (translation.y < -50 && currentState == .collapsed) {
                expandBottomSheet()
            } else if velocity.y > 500 || (translation.y > 50 && currentState == .expanded) {
                collapseBottomSheet()
            } else {
                // Snap back to current state
                animateToState(currentState)
            }
        default:
            break
        }
    }

    @objc private func handleTapGesture(_ gesture: UITapGestureRecognizer) {
        if currentState == .collapsed {
            expandBottomSheet()
        } else {
            collapseBottomSheet()
        }
    }

    private func expandBottomSheet() {
        currentState = .expanded
        animateToState(.expanded)
        delegate?.bottomSheetDidChangeState(self, state: .expanded)
    }

    private func collapseBottomSheet() {
        currentState = .collapsed
        animateToState(.collapsed)
        delegate?.bottomSheetDidChangeState(self, state: .collapsed)
    }

    private func animateToState(_ state: BottomSheetState) {
        let height = state == .expanded ? expandedHeight : collapsedHeight
        let alpha: CGFloat = state == .expanded ? 1.0 : 0.0

        UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseInOut) {
            self.containerView.constraints.first { $0.firstAttribute == .height }?.constant = height
            self.transportModeSelector.alpha = alpha
            self.headerLabel.alpha = alpha
            self.tableView.alpha = alpha
            self.view.layoutIfNeeded()
        }
    }

    func updateParkingSpots(_ spots: [ParkingSpot]) {
        parkingSpots = spots
        tableView.reloadData()
    }
}

// MARK: - UITableViewDataSource
extension BottomSheetViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return parkingSpots.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: ParkingListCell.identifier, for: indexPath) as? ParkingListCell else {
            return UITableViewCell()
        }

        let parkingSpot = parkingSpots[indexPath.row]
        cell.configure(with: parkingSpot)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension BottomSheetViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let parkingSpot = parkingSpots[indexPath.row]
        delegate?.bottomSheetDidSelectParking(self, parkingSpot: parkingSpot)
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 84
    }
}

// MARK: - TransportModeSelectorDelegate
extension BottomSheetViewController: TransportModeSelectorDelegate {
    func transportModeSelector(_ selector: TransportModeSelector, didSelectMode mode: TransportMode) {
        selectedTransportMode = mode
        headerLabel.text = mode.parkingTitle
        // Here you could filter parking spots based on transport mode
    }
}

// MARK: - UISearchBarDelegate
extension BottomSheetViewController: UISearchBarDelegate {
    func searchBar(_ searchBar: UISearchBar, textDidChange searchText: String) {
        // Implement search functionality
        if searchText.isEmpty {
            updateParkingSpots(ParkingSpot.sampleData)
        } else {
            let filteredSpots = ParkingSpot.sampleData.filter { spot in
                spot.name.localizedCaseInsensitiveContains(searchText) ||
                spot.address.localizedCaseInsensitiveContains(searchText)
            }
            updateParkingSpots(filteredSpots)
        }
    }

    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
    }
}
