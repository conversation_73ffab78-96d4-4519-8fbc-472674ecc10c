//
//  ParkingAnnotation.swift
//  XYZ Car Parking
//
//  Created by <PERSON><PERSON><PERSON> on 5/8/2025.
//

import MapKit

class ParkingAnnotation: NSObject, MKAnnotation {
    let parkingSpot: ParkingSpot
    
    var coordinate: CLLocationCoordinate2D {
        return parkingSpot.coordinate
    }
    
    var title: String? {
        return parkingSpot.name
    }
    
    var subtitle: String? {
        return "\(parkingSpot.availableSpots) spots available"
    }
    
    init(parkingSpot: ParkingSpot) {
        self.parkingSpot = parkingSpot
        super.init()
    }
}

class ParkingAnnotationView: MKAnnotationView {
    static let identifier = "ParkingAnnotationView"
    
    override init(annotation: MKAnnotation?, reuseIdentifier: String?) {
        super.init(annotation: annotation, reuseIdentifier: reuseIdentifier)
        setupView()
    }
    
    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
        setupView()
    }
    
    private func setupView() {
        canShowCallout = true
        
        // Create red circular marker
        let circleView = UIView(frame: CGRect(x: 0, y: 0, width: 20, height: 20))
        circleView.backgroundColor = UIColor.systemRed
        circleView.layer.cornerRadius = 10
        circleView.layer.borderWidth = 2
        circleView.layer.borderColor = UIColor.white.cgColor
        
        // Add shadow
        circleView.layer.shadowColor = UIColor.black.cgColor
        circleView.layer.shadowOffset = CGSize(width: 0, height: 2)
        circleView.layer.shadowOpacity = 0.3
        circleView.layer.shadowRadius = 2
        
        addSubview(circleView)
        frame = circleView.frame
        
        // Add info button for callout
        let infoButton = UIButton(type: .detailDisclosure)
        rightCalloutAccessoryView = infoButton
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        // Reset any custom state if needed
    }
}
